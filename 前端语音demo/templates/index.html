<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时语音转写</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .status-container {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        #status {
            margin-left: 10px;
            font-weight: bold;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ccc;
        }
        .status-dot.connected {
            background-color: #2ecc71;
        }
        .status-dot.disconnected {
            background-color: #e74c3c;
        }
        .status-dot.recording {
            background-color: #f39c12;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .controls {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        button.recording {
            background-color: #e74c3c;
        }
        .result-container {
            margin-top: 20px;
        }
        #result {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            line-height: 1.5;
            resize: vertical;
            background-color: #f9f9f9;
        }
        .log-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            height: 150px;
            overflow-y: auto;
            background-color: #f9f9f9;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 3px 0;
            border-bottom: 1px solid #eee;
        }
        .log-time {
            color: #7f8c8d;
            margin-right: 10px;
        }
        .log-info {
            color: #3498db;
        }
        .log-error {
            color: #e74c3c;
        }
        .log-success {
            color: #2ecc71;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>实时语音转写</h1>
        
        <div class="status-container">
            <div id="statusDot" class="status-dot disconnected"></div>
            <div id="status">未连接</div>
        </div>
        
        <div class="controls">
            <button id="connectButton" onclick="connectWebSocket()">连接服务器</button>
            <button id="startButton" onclick="startRecording()" disabled>开始录音</button>
            <button id="stopButton" onclick="stopRecording()" disabled>停止录音</button>
            <button id="disconnectButton" onclick="disconnectWebSocket()" disabled>断开连接</button>
            <button id="clearButton" onclick="clearResults()">清除结果</button>
            <button id="copyButton" onclick="copyResults()">复制结果</button>
            <button id="saveButton" onclick="saveResults()">保存结果</button>
        </div>
        
        <div class="result-container">
            <textarea id="result" placeholder="转写结果将显示在这里..." readonly></textarea>
        </div>
        
        <div class="log-container" id="logContainer"></div>
    </div>

    <script>
        let websocket;
        let audioContext;
        let scriptProcessor;
        let audioInput;
        let audioStream;
        let isRecording = false;
        let token = '';
        let appKey = '';
        
        // 页面加载完成后自动获取Token
        window.onload = function() {
            fetchToken();
        };
        
        // 获取Token
        async function fetchToken() {
            try {
                logMessage('正在获取Token...', 'info');
                const response = await fetch('/api/token');
                const data = await response.json();
                
                if (data.token && data.appKey) {
                    token = data.token;
                    appKey = data.appKey;
                    logMessage(`Token获取成功，有效期至: ${new Date(data.expireTime * 1000).toLocaleString()}`, 'success');
                    document.getElementById('connectButton').disabled = false;
                } else {
                    logMessage('获取Token失败: ' + JSON.stringify(data), 'error');
                }
            } catch (error) {
                logMessage('获取Token出错: ' + error.message, 'error');
            }
        }
        
        // 更新连接状态
        function updateStatus(status, isConnected = false, isRecording = false) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('status');
            
            statusText.textContent = status;
            
            statusDot.className = 'status-dot';
            if (isRecording) {
                statusDot.classList.add('recording');
            } else if (isConnected) {
                statusDot.classList.add('connected');
            } else {
                statusDot.classList.add('disconnected');
            }
        }
        
        // 生成UUID
        function generateUUID() {
            return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
                (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
            ).replace(/-/g, '');
        }
        
        // 打开WebSocket连接
        function connectWebSocket() {
            if (!token || !appKey) {
                logMessage('Token或AppKey未获取，请先获取Token', 'error');
                return;
            }
            
            const socketUrl = `wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1?token=${token}`;
            logMessage(`正在连接WebSocket服务器: ${socketUrl}`, 'info');
            
            websocket = new WebSocket(socketUrl);
            
            websocket.onopen = function() {
                updateStatus('已连接', true);
                logMessage('WebSocket连接成功', 'success');
                
                // 发送开始转写指令
                const startTranscriptionMessage = {
                    header: {
                        appkey: appKey,
                        namespace: "SpeechTranscriber",
                        name: "StartTranscription",
                        task_id: generateUUID(),
                        message_id: generateUUID()
                    },
                    payload: {
                        format: "pcm",
                        sample_rate: 16000,
                        enable_intermediate_result: true,
                        enable_punctuation_prediction: true,
                        enable_inverse_text_normalization: true
                    }
                };
                
                websocket.send(JSON.stringify(startTranscriptionMessage));
                logMessage('已发送开始转写指令', 'info');
            };
            
            websocket.onmessage = function(event) {
                const message = JSON.parse(event.data);
                logMessage(`收到消息: ${message.header.name}`, 'info');
                
                // 处理不同类型的消息
                switch (message.header.name) {
                    case "TranscriptionStarted":
                        logMessage('转写服务已准备就绪', 'success');
                        document.getElementById('startButton').disabled = false;
                        document.getElementById('disconnectButton').disabled = false;
                        break;
                        
                    case "TranscriptionResultChanged":
                        // 不再更新中间结果，只在日志中显示
                        if (message.payload && message.payload.result) {
                            logMessage(`中间结果: ${message.payload.result}`, 'info');
                        }
                        break;
                        
                    case "SentenceEnd":
                        // 只更新最终结果
                        if (message.payload && message.payload.result) {
                            const currentText = document.getElementById('result').value;
                            const newText = message.payload.result;
                            
                            // 如果当前文本不包含新文本，则追加
                            if (!currentText.includes(newText)) {
                                document.getElementById('result').value = currentText + '\n' + newText;
                                logMessage(`最终结果: ${newText}`, 'success');
                            }
                        }
                        break;
                        
                    case "TranscriptionCompleted":
                        logMessage('转写已完成', 'success');
                        break;
                }
            };
            
            websocket.onerror = function(event) {
                updateStatus('连接错误');
                logMessage('WebSocket错误: ' + JSON.stringify(event), 'error');
            };
            
            websocket.onclose = function() {
                updateStatus('已断开连接');
                logMessage('WebSocket连接已关闭', 'info');
                document.getElementById('startButton').disabled = true;
                document.getElementById('stopButton').disabled = true;
                document.getElementById('disconnectButton').disabled = true;
                
                // 如果正在录音，停止录音
                if (isRecording) {
                    stopRecording();
                }
            };
            
            document.getElementById('connectButton').disabled = true;
        }
        
        // 断开WebSocket连接
        function disconnectWebSocket() {
            if (websocket) {
                // 如果正在录音，先停止录音
                if (isRecording) {
                    stopRecording();
                }
                
                // 发送停止转写指令
                const stopTranscriptionMessage = {
                    header: {
                        appkey: appKey,
                        namespace: "SpeechTranscriber",
                        name: "StopTranscription",
                        task_id: generateUUID(),
                        message_id: generateUUID()
                    }
                };
                
                websocket.send(JSON.stringify(stopTranscriptionMessage));
                logMessage('已发送停止转写指令', 'info');
                
                // 关闭WebSocket连接
                websocket.close();
            }
            
            document.getElementById('connectButton').disabled = false;
            document.getElementById('disconnectButton').disabled = true;
        }
        
        // 开始录音
        async function startRecording() {
            try {
                logMessage('正在请求麦克风权限...', 'info');
                
                // 获取音频输入设备
                audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                audioContext = new (window.AudioContext || window.webkitAudioContext)({
                    sampleRate: 16000
                });
                audioInput = audioContext.createMediaStreamSource(audioStream);
                
                // 设置缓冲区大小为2048的脚本处理器
                scriptProcessor = audioContext.createScriptProcessor(2048, 1, 1);
                
                scriptProcessor.onaudioprocess = function(event) {
                    const inputData = event.inputBuffer.getChannelData(0);
                    const inputData16 = new Int16Array(inputData.length);
                    
                    // 将Float32Array转换为Int16Array (PCM 16-bit)
                    for (let i = 0; i < inputData.length; ++i) {
                        inputData16[i] = Math.max(-1, Math.min(1, inputData[i])) * 0x7FFF;
                    }
                    
                    // 发送音频数据
                    if (websocket && websocket.readyState === WebSocket.OPEN) {
                        websocket.send(inputData16.buffer);
                    }
                };
                
                audioInput.connect(scriptProcessor);
                scriptProcessor.connect(audioContext.destination);
                
                isRecording = true;
                updateStatus('正在录音', true, true);
                document.getElementById('startButton').disabled = true;
                document.getElementById('stopButton').disabled = false;
                document.getElementById('startButton').classList.add('recording');
                
                logMessage('录音已开始', 'success');
            } catch (e) {
                logMessage('录音失败: ' + e.message, 'error');
            }
        }
        
        // 停止录音
        function stopRecording() {
            if (scriptProcessor) {
                scriptProcessor.disconnect();
            }
            if (audioInput) {
                audioInput.disconnect();
            }
            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop());
            }
            if (audioContext) {
                audioContext.close();
            }
            
            isRecording = false;
            updateStatus('已连接', true);
            document.getElementById('startButton').disabled = false;
            document.getElementById('stopButton').disabled = true;
            document.getElementById('startButton').classList.remove('recording');
            
            logMessage('录音已停止', 'info');
        }
        
        // 日志消息
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            const time = document.createElement('span');
            time.className = 'log-time';
            time.textContent = `[${new Date().toLocaleTimeString()}] `;
            
            const content = document.createElement('span');
            content.className = `log-${type}`;
            content.textContent = message;
            
            logEntry.appendChild(time);
            logEntry.appendChild(content);
            logContainer.appendChild(logEntry);
            
            // 滚动到底部
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 清除结果
        function clearResults() {
            document.getElementById('result').value = '';
            logMessage('已清除转写结果', 'info');
        }
        
        // 复制结果到剪贴板
        function copyResults() {
            const resultText = document.getElementById('result').value;
            if (!resultText.trim()) {
                logMessage('没有可复制的结果', 'error');
                return;
            }
            
            navigator.clipboard.writeText(resultText)
                .then(() => {
                    logMessage('结果已复制到剪贴板', 'success');
                })
                .catch(err => {
                    logMessage('复制失败: ' + err.message, 'error');
                });
        }
        
        // 保存结果到文件
        function saveResults() {
            const resultText = document.getElementById('result').value;
            if (!resultText.trim()) {
                logMessage('没有可保存的结果', 'error');
                return;
            }
            
            // 创建Blob对象
            const blob = new Blob([resultText], { type: 'text/plain;charset=utf-8' });
            
            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            
            // 设置文件名
            const now = new Date();
            const fileName = `语音转写结果_${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}.txt`;
            a.download = fileName;
            
            // 触发下载
            document.body.appendChild(a);
            a.click();
            
            // 清理
            setTimeout(() => {
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 100);
            
            logMessage(`结果已保存为文件: ${fileName}`, 'success');
        }
    </script>
</body>
</html> 