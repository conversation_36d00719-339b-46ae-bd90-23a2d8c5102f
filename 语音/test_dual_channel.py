#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试双轨音频处理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from batch_convert import format_dialogue_from_sentences

def test_format_dialogue():
    """测试对话格式化函数"""
    
    # 模拟双轨音频识别结果
    test_sentences = [
        {
            "text": "哎，",
            "begin_time": 2720,
            "end_time": 3046,
            "channel_id": 0
        },
        {
            "text": "你好，",
            "begin_time": 3046,
            "end_time": 3698,
            "channel_id": 0
        },
        {
            "text": "我是医生。",
            "begin_time": 3698,
            "end_time": 5004,
            "channel_id": 0
        },
        {
            "text": "你好医生，",
            "begin_time": 5500,
            "end_time": 6200,
            "channel_id": 1
        },
        {
            "text": "我最近感觉不舒服。",
            "begin_time": 6200,
            "end_time": 8000,
            "channel_id": 1
        },
        {
            "text": "哦好的啊，",
            "begin_time": 231220,
            "end_time": 232980,
            "channel_id": 0
        },
        {
            "text": "具体哪里不舒服？",
            "begin_time": 232980,
            "end_time": 234000,
            "channel_id": 0
        },
        {
            "text": "肚子疼，",
            "begin_time": 234500,
            "end_time": 235200,
            "channel_id": 1
        },
        {
            "text": "还有点发烧。",
            "begin_time": 235200,
            "end_time": 236500,
            "channel_id": 1
        }
    ]
    
    # 测试格式化函数
    continuous_text, formatted_dialogue = format_dialogue_from_sentences(test_sentences)
    
    print("=== 原始连续文本 ===")
    print(continuous_text)
    print("\n=== 格式化对话 ===")
    print(formatted_dialogue)
    
    # 验证结果
    expected_continuous = "哎，你好，我是医生。你好医生，我最近感觉不舒服。哦好的啊，具体哪里不舒服？肚子疼，还有点发烧。"
    expected_dialogue = """医生：哎，你好，我是医生。
患者：你好医生，我最近感觉不舒服。
医生：哦好的啊，具体哪里不舒服？
患者：肚子疼，还有点发烧。
"""
    
    print("\n=== 验证结果 ===")
    print(f"连续文本匹配: {continuous_text == expected_continuous}")
    print(f"对话格式匹配: {formatted_dialogue == expected_dialogue}")
    
    if continuous_text == expected_continuous and formatted_dialogue == expected_dialogue:
        print("✅ 测试通过！")
    else:
        print("❌ 测试失败！")
        print(f"期望连续文本: {expected_continuous}")
        print(f"实际连续文本: {continuous_text}")
        print(f"期望对话格式:\n{expected_dialogue}")
        print(f"实际对话格式:\n{formatted_dialogue}")

if __name__ == "__main__":
    test_format_dialogue()
