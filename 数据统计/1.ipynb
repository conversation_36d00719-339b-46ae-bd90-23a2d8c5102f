#%%
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix

#%%
# 定义一个函数用于绘制混淆矩阵
def plot_confusion_matrix(cm, classes,
                          normalize=False,
                          title='Confusion matrix',
                          cmap=plt.cm.Blues):
    """
    This function prints and plots the confusion matrix.
    Normalization can be applied by setting `normalize=True`.
    """
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        print("Normalized confusion matrix")
    else:
        print('Confusion matrix, without normalization')

    print(cm)

    plt.imshow(cm, interpolation='nearest', cmap=cmap)
    plt.title(title)
    plt.colorbar()
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes, rotation=45)
    plt.yticks(tick_marks, classes)

    fmt = '.2f' if normalize else 'd'
    thresh = cm.max() / 2.
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            plt.text(j, i, format(cm[i, j], fmt),
                     horizontalalignment="center",
                     color="white" if cm[i, j] > thresh else "black")

    plt.ylabel('True label')
    plt.xlabel('Predicted label')
    plt.tight_layout()
#%%
actual_values = [[0, 0, 1, 1], [1, 0, 1, 0], [0, 1, 1, 0]]
predicted_values = [[1, 1, 0, 0], [1, 0, 1, 0], [0, 0, 1, 1]]
#%%
conf_matrix = confusion_matrix(np.array(actual_values).flatten(), np.array(predicted_values).flatten())
#%% md

#%%
# 定义类别标签
classes = ['Class 0', 'Class 1']
#%%
# 绘制混淆矩阵（未归一化）
plt.figure()
#%%
print(conf_matrix)
#%%
plot_confusion_matrix(conf_matrix, classes=classes,
                      title='Confusion matrix, without normalization')

#%%
true_labels = np.random.randint(0, 5, size=100)  # 真实标签
predicted_labels = np.random.randint(0, 5, size=100)  # 预测标签

#%%
import seaborn as sns
# 实际值和预测值
actual_values = [[0, 0, 1, 1], [1, 0, 1, 1], [0, 1, 0, 0], [1, 0, 1, 0]]
predicted_values = [[0, 0, 1, 1], [0, 0, 1, 0], [0, 1, 0, 0], [1, 0, 1, 1]]

# 将列表转换为NumPy数组
actual_values = np.array(actual_values)
predicted_values = np.array(predicted_values)

# 计算混淆矩阵
cm = confusion_matrix(actual_values.flatten(), predicted_values.flatten())


plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, cmap='Blues', fmt='g', xticklabels=np.arange(0, 2), yticklabels=np.arange(0, 2))
plt.xlabel('Predicted labels')
plt.ylabel('True labels')
plt.title('Confusion Matrix')
plt.show()
#%%
