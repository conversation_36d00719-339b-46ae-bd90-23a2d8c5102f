#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量语音转文字处理脚本
功能：批量下载音频文件，进行语音识别和文本总结，并保存到Excel文件
"""

import os
import json
import tempfile
import shutil
import requests
import pandas as pd
from urllib.parse import urlparse
import http.client
from openai import OpenAI
from datetime import datetime
import time

# 阿里云语音识别配置
ALI_APP_KEY = "mfTTVM63gDa5Kg3O"
ALI_TOKEN = "58eba761d70d4898a5000fca7ba946de"

# OpenAI大模型配置
MODEL_API_BASE = "https://one-api.igancao.cn/v1"
CHAT_KEY = "sk-8O7LaJ7d4YP8JuPN16Fa330664924811A10aF4Ad1eFeA1Cc"
#CHAT_MODEL = "qwen3-235b-a22b"
CHAT_MODEL = "deepseek-r1"

PROMPT = """你是一个经验丰富的中医临床医生。现在你将收到一段中医医生问诊的语音转文字记录。这段文字是通过语音识别软件自动生成的双轨音频对话，其中：
- 医生：代表问诊的中医医生
- 患者：代表就诊的患者

文本可能存在一些识别错误，特别是在专业术语、药品名称和症状描述方面。

请你首先理解并纠正文本中可能的识别错误，然后按照以下格式生成规范的门诊病历：

主诉：
现病史：
刻下症状：
既往史：

在处理文本时，请注意：
1. 所有描述必须有原文依据 严禁添加原文未明确提及的任何信息 宁可简单也不要臆想 如果原文信息不足，在相应部分注明"信息不详" 
2. 这是一段口语化的问诊对话，需要将其转换为规范的医学术语
3. 由于语音识别可能存在错误，请根据上下文和专业知识推断和纠正：
   - 症状描述
   - 药品名称
   - 时间描述（确保时间的连贯性和准确性）
4. 将口语化表达转换为中西医学术语，例如：
   - "拉肚子" → "腹泻"
   - "发烧" → "发热"
   - "怕冷" → "恶寒"
   - "吐" → "呕吐"
   - "胃痛" → "胃部疼痛"
   

各部分要求：
一、主诉要求：
1. 核心组成：提取1-3个主要症状或体征，突出病变关键矛盾
2. 记录规范：
   - 语言精简，通常不超过20字，用患者描述的客观现象而非诊断结论，如"多饮多尿1年"或"发现血糖升高1年"而非"糖尿病1年"。
   - 使用客观症状描述，避免诊断性用语
   - 准确记录时间：慢性病用年/月，急症用天/小时
3. 临床意义
主诉提示疾病的病位、病性及进展，例如心悸胸痛多指向心脏问题，咳嗽痰多提示肺系疾病。特殊情况下（如体检发现异常），主诉可为"发现子宫肌瘤20天"，此时病名需加引号。
示例：若患者因发热就诊，主诉应写"发热伴咳嗽3天"，而非"风热感冒3天"。

二、现病史要求：
1. 起病情况：
   - 记录起病时间
   - 可能的诱因（如情志、饮食、外感等）例如，"3天前受凉后出现恶寒发热"。
   - 最初症状表现
2. 症状演变与特点：
   - 症状的变化过程（如加重、减轻、新发症状） 
   - 伴随症状的出现顺序（如初始为干咳，后转为咳黄痰。）
3. 伴随症状：
   - 记录与主症同时出现的其他症状，协助鉴别诊断。（如腹痛伴腹泻、里急后重提示痢疾；腹痛伴呕吐、黄疸需考虑肝胆疾病。）
4. 治疗经过：
   - 包括前期就诊记录（如西医检查结果、中医方药名称及疗效）与治疗反应。（如"曾服桂枝汤后汗出热退，但次日复发"。）
5. 一般情况：
   - 涵盖发病后饮食、睡眠、二便、体重等整体状态的变化，评估疾病对机体的整体影响。

三、刻下症状要求：
1. 详细记录就诊时的主要临床表现。要求尽量详细，聚焦当前病理状态，是辨证的直接依据。：


四、既往史要求：
1. 记录既往重要疾病史包括过去的重大疾病（如传染性疾病、慢性病）
2. 手术外伤史
3. 过敏史
4. 尤其关注与本次就诊相关的既往情况

请记住：
1. 保持专业性：将口语化描述转换为规范医学术语
2. 保持完整性：完整记录症状发展过程
3. 保持逻辑性：各症状之间的关联和发展要有逻辑关系
4. 时间准确性：注意记录各症状的具体发生时间和持续时间"""

# 初始化OpenAI客户端
openai_client = OpenAI(
    api_key=CHAT_KEY,
    base_url=MODEL_API_BASE,
)

def download_audio_from_url(url, timeout=30):
    """
    从URL下载音频文件到临时文件
    
    参数:
        url: 音频文件URL
        timeout: 下载超时时间（秒）
    
    返回:
        临时文件路径，如果下载失败返回None
    """
    try:
        print(f"正在下载音频: {url}")
        
        # 解析URL获取文件扩展名
        parsed_url = urlparse(url)
        file_extension = os.path.splitext(parsed_url.path)[1].lower()
        if not file_extension:
            file_extension = '.mp3'  # 默认扩展名
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_extension)
        
        # 下载文件
        response = requests.get(url, timeout=timeout, stream=True)
        response.raise_for_status()
        
        # 写入临时文件
        for chunk in response.iter_content(chunk_size=8192):
            temp_file.write(chunk)
        
        temp_file.close()
        print(f"下载完成: {temp_file.name}")
        return temp_file.name
        
    except Exception as e:
        print(f"下载音频失败 {url}: {e}")
        if 'temp_file' in locals() and temp_file:
            temp_file.close()
            if os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
        return None

def recognize_audio(app_key, token, audio_file, format="mp3", sample_rate=8000, first_channel_only=None):
    """
    使用阿里云录音文件识别极速版API识别音频文件

    参数:
        app_key: 阿里云应用的Appkey
        token: 访问令牌
        audio_file: 音频文件路径
        format: 音频格式，支持MP4、AAC、MP3、OPUS、WAV，默认mp3
        sample_rate: 采样率，8000(电话)或16000(非电话)，默认16000
        first_channel_only: 是否只识别首个声道，取值：true/false
                          - None(默认): 8k处理双声道，16k处理单声道
                          - True: 8k处理单声道，16k处理单声道
                          - False: 8k处理双声道，16k处理双声道
                          注意：多声道会叠加计费，双声道为双倍计费

    返回:
        识别结果的JSON对象
    """
    try:
        # 读取音频文件
        with open(audio_file, mode='rb') as f:
            audio_content = f.read()
        
        # 服务地址
        host = 'nls-gateway-cn-shanghai.aliyuncs.com'
        
        # 设置HTTP请求头部
        http_headers = {
            'Content-Type': 'application/octet-stream',
            'Content-Length': str(len(audio_content))
        }
        
        # 构建请求URL
        url = f'/stream/v1/FlashRecognizer?appkey={app_key}&token={token}&format={format}&sample_rate={sample_rate}'

        # 添加双轨音频处理参数
        if first_channel_only is not None:
            url += f'&first_channel_only={str(first_channel_only).lower()}'
        
        # 发送HTTPS请求
        conn = http.client.HTTPSConnection(host)
        conn.request(method='POST', url=url, body=audio_content, headers=http_headers)
        
        # 获取响应
        response = conn.getresponse()
        print(f'语音识别响应状态: {response.status} {response.reason}')
        
        body = response.read()
        conn.close()
        
        # 解析JSON响应
        result = json.loads(body)
        return result
        
    except Exception as e:
        print(f'语音识别失败: {e}')
        return None

def format_dialogue_from_sentences(sentences):
    """
    将双轨音频的句子列表格式化为清晰的对话形式

    参数:
        sentences: 包含channel_id的句子列表

    返回:
        tuple: (原始连续文本, 格式化对话文本)
    """
    if not sentences:
        return "", ""

    # 按时间顺序排序句子
    sorted_sentences = sorted(sentences, key=lambda x: x["begin_time"])

    # 初始化变量
    continuous_text = ""
    formatted_dialogue = ""
    current_speaker = None
    current_content = ""

    for sentence in sorted_sentences:
        channel_id = sentence.get("channel_id", 0)
        text_content = sentence["text"].strip()

        if not text_content:
            continue

        # 确定角色
        if channel_id == 0:
            speaker = "医生"
        elif channel_id == 1:
            speaker = "患者"
        else:
            speaker = f"角色{channel_id}"

        # 添加到连续文本
        continuous_text += text_content

        # 处理对话格式化
        if current_speaker == speaker:
            # 同一说话者，继续累积内容
            current_content += text_content
        else:
            # 不同说话者，输出之前的内容并开始新的
            if current_speaker is not None and current_content:
                formatted_dialogue += f"{current_speaker}：{current_content}\n"
            current_speaker = speaker
            current_content = text_content

    # 输出最后一个说话者的内容
    if current_speaker is not None and current_content:
        formatted_dialogue += f"{current_speaker}：{current_content}\n"

    return continuous_text, formatted_dialogue

def summarize_text(text):
    """
    使用大模型对文本进行总结
    """
    try:
        print("正在调用大模型进行文本总结...")
        completion = openai_client.chat.completions.create(
            model=CHAT_MODEL,
            messages=[
                {"role": "system", "content": PROMPT},
                {"role": "user", "content": text}
            ],
            stream=False,
            extra_body={"enable_thinking": False}
        )
        
        summary = completion.choices[0].message.content
        print("文本总结完成")
        return summary
    except Exception as e:
        print(f"调用大模型总结失败: {e}")
        return "大模型总结失败，请查看原文。"

def process_audio_url(url):
    """
    处理单个音频URL：下载、识别、总结
    
    参数:
        url: 音频URL
    
    返回:
        dict: 包含处理结果的字典
    """
    result = {
        'url': url,
        'success': False,
        'text': '',
        'formatted_dialogue': '',
        'summary': '',
        'error': ''
    }
    
    temp_file = None
    try:
        # 1. 下载音频文件
        temp_file = download_audio_from_url(url)
        if not temp_file:
            result['error'] = '音频下载失败'
            return result
        
        # 2. 获取音频格式
        file_extension = os.path.splitext(temp_file)[1].lower().replace('.', '')
        if file_extension not in ['wav', 'mp3', 'aac', 'opus', 'mp4']:
            file_extension = 'mp3'  # 默认格式
        
        # 3. 语音识别
        print(f"正在进行语音识别...")
        # 对于双轨录音，建议设置 first_channel_only=True 避免重复识别和双倍计费
        recognition_result = recognize_audio(ALI_APP_KEY, ALI_TOKEN, temp_file, format=file_extension, first_channel_only=False)
        
        if recognition_result and recognition_result.get("status") == 20000000:
            # 提取识别文本
            if "flash_result" in recognition_result and "sentences" in recognition_result["flash_result"]:
                sentences = recognition_result["flash_result"]["sentences"]

                # 使用新的格式化函数处理对话
                text, formatted_dialogue = format_dialogue_from_sentences(sentences)

                result['text'] = text
                result['formatted_dialogue'] = formatted_dialogue
            else:
                result['text'] = ""
                result['formatted_dialogue'] = ""
            
            # 4. 文本总结
            if text:
                # 优先使用格式化的对话内容进行总结
                content_for_summary = formatted_dialogue if formatted_dialogue else text
                summary = summarize_text(content_for_summary)
                result['summary'] = summary
                result['success'] = True
            else:
                result['error'] = '语音识别结果为空'
        else:
            error_msg = recognition_result.get("message", "识别失败") if recognition_result else "识别失败"
            result['error'] = f'语音识别失败: {error_msg}'
            
    except Exception as e:
        result['error'] = f'处理过程中发生错误: {str(e)}'
    finally:
        # 清理临时文件
        if temp_file and os.path.exists(temp_file):
            os.unlink(temp_file)
    
    return result

def batch_convert_audio_urls(audio_urls, output_file=None):
    """
    批量处理音频URL列表
    
    参数:
        audio_urls: 音频URL列表
        output_file: 输出Excel文件路径，如果为None则自动生成
    
    返回:
        处理结果列表
    """
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"语音转换结果_{timestamp}.xlsx"
    
    print(f"开始批量处理 {len(audio_urls)} 个音频文件...")
    
    results = []
    for i, url in enumerate(audio_urls, 1):
        print(f"\n处理第 {i}/{len(audio_urls)} 个音频: {url}")
        
        result = process_audio_url(url)
        results.append(result)
        
        # 添加延时避免请求过于频繁
        if i < len(audio_urls):
            time.sleep(1)
    
    # 保存结果到Excel
    save_results_to_excel(results, output_file)
    
    print(f"\n批量处理完成！结果已保存到: {output_file}")
    return results

def save_results_to_excel(results, output_file):
    """
    将处理结果保存到Excel文件
    
    参数:
        results: 处理结果列表
        output_file: 输出文件路径
    """
    # 准备数据
    data = []
    for result in results:
        row = {
            '音频URL': result['url'],
            '处理状态': '成功' if result['success'] else '失败',
            '识别文本': result['text'],
            '格式化对话': result.get('formatted_dialogue', ''),
            '总结结果': result['summary'],
            '错误信息': result['error']
        }
        data.append(row)
    
    # 创建DataFrame并保存
    df = pd.DataFrame(data)
    df.to_excel(output_file, index=False, engine='openpyxl')
    print(f"结果已保存到Excel文件: {output_file}")

if __name__ == "__main__":
    # 示例音频URL列表
    audio_urls = [
        'https://storage1.igancao.com/GC/video/40/45/d5e43a5669d2d486a89e6faae197b780.mp3',
        'https://storage1.igancao.com/GC/video/BE/BF/9d4f36b1e7b5dbe177f9ed7db645bd77.mp3'
    ]
    
    # 执行批量转换
    batch_convert_audio_urls(audio_urls)
