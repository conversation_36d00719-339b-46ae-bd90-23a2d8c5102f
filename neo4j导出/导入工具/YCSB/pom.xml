<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (c) 2012 - 2018 YCSB contributors. All rights reserved.

Licensed under the Apache License, Version 2.0 (the "License"); you
may not use this file except in compliance with the License. You
may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
implied. See the License for the specific language governing
permissions and limitations under the License. See accompanying
LICENSE file.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.yahoo.ycsb</groupId>
  <artifactId>root</artifactId>
  <version>0.17.0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <name>YCSB Root</name>

  <description>
    This is the top level project that builds, packages the core and all the DB bindings for YCSB infrastructure.
  </description>

  <scm>
    <connection>scm:git:git://github.com/brianfrankcooper/YCSB.git</connection>
    <tag>master</tag>
    <url>https://github.com/brianfrankcooper/YCSB</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.puppycrawl.tools</groupId>
        <artifactId>checkstyle</artifactId>
        <version>7.7.1</version>
      </dependency>
      <dependency>
        <groupId>org.jdom</groupId>
        <artifactId>jdom</artifactId>
        <version>1.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.collections</groupId>
        <artifactId>google-collections</artifactId>
        <version>1.0</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.25</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <!-- Properties Management -->
  <properties>
    <maven.assembly.version>2.5.5</maven.assembly.version>
    <maven.dependency.version>2.10</maven.dependency.version>

    <!-- datastore binding versions, lex sorted -->
    <accumulo.1.6.version>1.6.6</accumulo.1.6.version>
    <accumulo.1.7.version>1.7.4</accumulo.1.7.version>
    <accumulo.1.8.version>1.9.1</accumulo.1.8.version>
    <aerospike.version>3.1.2</aerospike.version>
    <arangodb.version>4.4.1</arangodb.version>
    <asynchbase.version>1.8.2</asynchbase.version>
    <azurecosmos.version>2.2.3</azurecosmos.version>
    <azurestorage.version>4.0.0</azurestorage.version>
    <cassandra.cql.version>3.0.0</cassandra.cql.version>
    <cloudspanner.version>0.36.0-beta</cloudspanner.version>
    <couchbase.version>1.4.10</couchbase.version>
		<couchbase2.version>2.3.1</couchbase2.version>
		<crail.version>1.1-incubating</crail.version>
    <elasticsearch5-version>5.5.1</elasticsearch5-version>
    <foundationdb.version>5.2.5</foundationdb.version>
    <geode.version>1.2.0</geode.version>
    <googlebigtable.version>1.4.0</googlebigtable.version>
    <griddb.version>4.0.0</griddb.version>
    <hbase098.version>0.98.14-hadoop2</hbase098.version>
    <hbase10.version>1.0.2</hbase10.version>
    <hbase12.version>1.2.5</hbase12.version>
    <hbase14.version>1.4.2</hbase14.version>
    <hbase20.version>2.0.0</hbase20.version>
    <hypertable.version>0.9.5.6</hypertable.version>
    <ignite.version>2.7.0</ignite.version>
    <infinispan.version>7.2.2.Final</infinispan.version>
    <kudu.version>1.6.0</kudu.version>
    <maprhbase.version>1.1.8-mapr-1710</maprhbase.version>
    <!--<mapkeeper.version>1.0</mapkeeper.version>-->
    <mongodb.version>3.8.0</mongodb.version>
    <mongodb.async.version>2.0.1</mongodb.async.version>
    <openjpa.jdbc.version>2.1.1</openjpa.jdbc.version>
    <orientdb.version>2.2.37</orientdb.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <redis.version>2.9.0</redis.version>
    <riak.version>2.0.5</riak.version>
    <rocksdb.version>5.11.3</rocksdb.version>
    <s3.version>1.10.20</s3.version>
    <solr.version>5.5.3</solr.version>
    <solr6.version>6.4.1</solr6.version>
    <tarantool.version>1.6.5</tarantool.version>
    <thrift.version>0.8.0</thrift.version>
    <voldemort.version>0.81</voldemort.version>
    <tablestore.version>4.8.0</tablestore.version>
  </properties>

  <modules>
    <!-- our internals -->
    <module>core</module>
    <module>binding-parent</module>
    <module>distribution</module>
    <module>gremlin</module>
    <!-- all the datastore bindings, lex sorted please -->
    <module>accumulo1.6</module>
    <module>accumulo1.7</module>
    <module>accumulo1.8</module>
    <module>aerospike</module>
    <module>arangodb</module>
    <module>asynchbase</module>
    <module>azurecosmos</module>
    <module>azuretablestorage</module>
    <module>cassandra</module>
    <module>cloudspanner</module>
    <module>couchbase</module>
		<module>couchbase2</module>
		<module>crail</module>
    <module>dynamodb</module>
    <module>elasticsearch</module>
    <module>elasticsearch5</module>
    <module>foundationdb</module>
    <module>geode</module>
    <module>googlebigtable</module>
    <module>googledatastore</module>
    <module>griddb</module>
    <module>hbase098</module>
    <module>hbase10</module>
    <module>hbase12</module>
    <module>hbase14</module>
    <module>hbase20</module>
    <module>hypertable</module>
    <module>ignite</module>
    <module>infinispan</module>
    <module>jdbc</module>
    <module>kudu</module>
    <!--<module>mapkeeper</module>-->
    <module>maprdb</module>
    <module>maprjsondb</module>
    <module>memcached</module>
    <module>mongodb</module>
    <module>nosqldb</module>
    <module>orientdb</module>
    <module>postgrenosql</module>
    <module>rados</module>
    <module>redis</module>
    <module>rest</module>
    <module>riak</module>
    <module>rocksdb</module>
    <module>s3</module>
    <module>solr</module>
    <module>solr6</module>
    <module>tarantool</module>
    <module>tablestore</module>
    <!--<module>voldemort</module>-->
  </modules>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>2.16</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.0.0-M1</version>
        <executions>
          <execution>
            <id>enforce-maven</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>3.1.0</version>
                </requireMavenVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.7.0</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <executions>
          <execution>
            <id>validate</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <configLocation>checkstyle.xml</configLocation>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
