import os
import json
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import http.client
import uvicorn
from starlette.requests import Request
import tempfile
import shutil
from openai import OpenAI

# 创建 FastAPI 应用
app = FastAPI(title="语音转文字服务")

# 配置跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# 配置模板
templates = Jinja2Templates(directory="app/templates")

# 阿里云语音识别配置
ALI_APP_KEY = "mfTTVM63gDa5Kg3O" # 需要替换为您的实际值
ALI_TOKEN = "58eba761d70d4898a5000fca7ba946de"    # 需要替换为您的实际值

# OpenAI大模型配置
MODEL_API_BASE = "https://one-api.igancao.cn/v1"
CHAT_KEY = "sk-8O7LaJ7d4YP8JuPN16Fa330664924811A10aF4Ad1eFeA1Cc"
CHAT_MODEL = "qwen3-235b-a22b"


PROMPT = """你是一个经验丰富的中医临床医生。现在你将收到一段中医医生问诊的语音转文字记录。这段文字是通过语音识别软件自动生成的，可能存在一些识别错误，特别是在专业术语、药品名称和症状描述方面。

请你首先理解并纠正文本中可能的识别错误，然后按照以下格式生成规范的门诊病历：

主诉：
现病史：
刻下症状：
既往史：

在处理文本时，请注意：
1. 所有描述必须有原文依据 严禁添加原文未明确提及的任何信息 宁可简单也不要臆想 如果原文信息不足，在相应部分注明"信息不详" 
2. 这是一段口语化的问诊对话，需要将其转换为规范的医学术语
3. 由于语音识别可能存在错误，请根据上下文和专业知识推断和纠正：
   - 症状描述
   - 药品名称
   - 时间描述（确保时间的连贯性和准确性）
4. 将口语化表达转换为中西医学术语，例如：
   - "拉肚子" → "腹泻"
   - "发烧" → "发热"
   - "怕冷" → "恶寒"
   - "吐" → "呕吐"
   - "胃痛" → "胃部疼痛"
   

各部分要求：
一、主诉要求：
1. 核心组成：提取1-3个主要症状或体征，突出病变关键矛盾
2. 记录规范：
   - 语言精简，通常不超过20字，用患者描述的客观现象而非诊断结论，如“多饮多尿1年”或“发现血糖升高1年”而非“糖尿病1年”。
   - 使用客观症状描述，避免诊断性用语
   - 准确记录时间：慢性病用年/月，急症用天/小时
3. 临床意义
主诉提示疾病的病位、病性及进展，例如心悸胸痛多指向心脏问题，咳嗽痰多提示肺系疾病。特殊情况下（如体检发现异常），主诉可为“发现子宫肌瘤20天”，此时病名需加引号。
示例：若患者因发热就诊，主诉应写“发热伴咳嗽3天”，而非“风热感冒3天”。

二、现病史要求：
1. 起病情况：
   - 记录起病时间
   - 可能的诱因（如情志、饮食、外感等）例如，“3天前受凉后出现恶寒发热”。
   - 最初症状表现
2. 症状演变与特点：
   - 症状的变化过程（如加重、减轻、新发症状） 
   - 伴随症状的出现顺序（如初始为干咳，后转为咳黄痰。）
3. 伴随症状：
   - 记录与主症同时出现的其他症状，协助鉴别诊断。（如腹痛伴腹泻、里急后重提示痢疾；腹痛伴呕吐、黄疸需考虑肝胆疾病。）
4. 治疗经过：
   - 包括前期就诊记录（如西医检查结果、中医方药名称及疗效）与治疗反应。（如“曾服桂枝汤后汗出热退，但次日复发”。）
5. 一般情况：
   - 涵盖发病后饮食、睡眠、二便、体重等整体状态的变化，评估疾病对机体的整体影响。

三、刻下症状要求：
1. 详细记录就诊时的主要临床表现。要求尽量详细，聚焦当前病理状态，是辨证的直接依据。：


四、既往史要求：
1. 记录既往重要疾病史包括过去的重大疾病（如传染性疾病、慢性病）
2. 手术外伤史
3. 过敏史
4. 尤其关注与本次就诊相关的既往情况

请记住：
1. 保持专业性：将口语化描述转换为规范医学术语
2. 保持完整性：完整记录症状发展过程
3. 保持逻辑性：各症状之间的关联和发展要有逻辑关系
4. 时间准确性：注意记录各症状的具体发生时间和持续时间"""

# 初始化OpenAI客户端
openai_client = OpenAI(
    api_key=CHAT_KEY,
    base_url=MODEL_API_BASE,
)

@app.get("/")
async def read_root(request: Request):
    """返回前端页面"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/convert")
async def convert_speech_to_text(file: UploadFile = File(...)):
    """
    接收音频文件并调用阿里云API进行识别，然后用大模型进行总结
    """
    # 检查文件类型
    allowed_formats = ["wav", "mp3", "aac", "opus", "mp4"]
    file_extension = file.filename.split(".")[-1].lower()
    
    if file_extension not in allowed_formats:
        raise HTTPException(status_code=400, detail=f"不支持的文件格式，仅支持: {', '.join(allowed_formats)}")
    
    # 保存上传的文件到临时目录
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f".{file_extension}")
    try:
        shutil.copyfileobj(file.file, temp_file)
        temp_file.close()
        
        # 调用阿里云语音识别API
        result = recognize_audio(ALI_APP_KEY, ALI_TOKEN, temp_file.name, format=file_extension)
        
        # 处理识别结果
        if result and result.get("status") == 20000000:
            text = ""
            if "flash_result" in result and "sentences" in result["flash_result"]:
                for sentence in result["flash_result"]["sentences"]:
                    text += sentence["text"]
            
            # 如果文本不为空，调用大模型进行总结
            summary = ""
            if text:
                summary = summarize_text(text)
                
            return JSONResponse(content={
                "success": True, 
                "text": text, 
                "summary": summary,
                "result": result
            })
        else:
            error_msg = result.get("message", "识别失败") if result else "识别失败"
            return JSONResponse(
                content={"success": False, "error": error_msg, "result": result}, 
                status_code=500
            )
    except Exception as e:
        return JSONResponse(
            content={"success": False, "error": str(e)}, 
            status_code=500
        )
    finally:
        # 删除临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

def recognize_audio(app_key, token, audio_file, format="wav", sample_rate=16000, first_channel_only=None):
    """
    使用阿里云录音文件识别极速版API识别音频文件

    参数:
        app_key: 阿里云应用的Appkey
        token: 访问令牌
        audio_file: 音频文件路径
        format: 音频格式，支持MP4、AAC、MP3、OPUS、WAV，默认wav
        sample_rate: 采样率，8000(电话)或16000(非电话)，默认16000
        first_channel_only: 是否只识别首个声道，取值：true/false
                          - None(默认): 8k处理双声道，16k处理单声道
                          - True: 8k处理单声道，16k处理单声道
                          - False: 8k处理双声道，16k处理双声道
                          注意：多声道会叠加计费，双声道为双倍计费

    返回:
        识别结果的JSON对象
    """
    # 读取音频文件
    with open(audio_file, mode='rb') as f:
        audio_content = f.read()

    # 服务地址
    host = 'nls-gateway-cn-shanghai.aliyuncs.com'

    # 设置HTTP请求头部
    http_headers = {
        'Content-Type': 'application/octet-stream',
        'Content-Length': str(len(audio_content))
    }

    # 构建请求URL
    url = f'/stream/v1/FlashRecognizer?appkey={app_key}&token={token}&format={format}&sample_rate={sample_rate}'

    # 添加双轨音频处理参数
    if first_channel_only is not None:
        url += f'&first_channel_only={str(first_channel_only).lower()}'
    
    # 发送HTTPS请求
    conn = http.client.HTTPSConnection(host)
    conn.request(method='POST', url=url, body=audio_content, headers=http_headers)
    
    # 获取响应
    response = conn.getresponse()
    print(f'响应状态: {response.status} {response.reason}')
    
    body = response.read()
    conn.close()
    
    # 解析JSON响应
    try:
        result = json.loads(body)
        return result
    except ValueError:
        print('返回结果不是有效的JSON格式')
        return None

def summarize_text(text):
    """
    使用大模型对文本进行总结
    """
    try:
        completion = openai_client.chat.completions.create(
            model=CHAT_MODEL,
            messages=[
                {"role": "system", "content": PROMPT},
                {"role": "user", "content": text}
            ],
            stream=False,
            extra_body={"enable_thinking": False}
        )
        
        summary = completion.choices[0].message.content
        return summary
    except Exception as e:
        print(f"调用大模型总结失败: {e}")
        return "大模型总结失败，请查看原文。"

if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)