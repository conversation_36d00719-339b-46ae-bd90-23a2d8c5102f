#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量语音转文字处理脚本 - 使用示例
"""

from batch_convert import batch_convert_audio_urls

def main():
    """
    主函数 - 在这里修改您的音频URL列表
    """
    
    # 在这里添加您的音频URL列表
    audio_urls = [
        'https://storage1.igancao.com/GC/video/40/45/d5e43a5669d2d486a89e6faae197b780.mp3',

    ]
    
    # 可选：指定输出文件名，如果不指定会自动生成带时间戳的文件名
    output_file = "语音转换结果.xlsx"
    
    print("=" * 60)
    print("批量语音转文字处理工具")
    print("=" * 60)
    print(f"待处理音频数量: {len(audio_urls)}")
    print(f"输出文件: {output_file}")
    print("=" * 60)
    
    # 执行批量转换
    try:
        results = batch_convert_audio_urls(audio_urls, output_file)
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        failed_count = len(results) - success_count
        
        print("\n" + "=" * 60)
        print("处理完成统计:")
        print(f"总数: {len(results)}")
        print(f"成功: {success_count}")
        print(f"失败: {failed_count}")
        print("=" * 60)
        
        if failed_count > 0:
            print("\n失败的音频:")
            for result in results:
                if not result['success']:
                    print(f"- {result['url']}: {result['error']}")
        
    except Exception as e:
        print(f"批量处理过程中发生错误: {e}")

if __name__ == "__main__":
    main()
