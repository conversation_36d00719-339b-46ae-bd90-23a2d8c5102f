import http.client
import json
import os

def recognize_audio(app_key, token, audio_file, format="wav", sample_rate=16000):
    """
    使用阿里云录音文件识别极速版API识别音频文件
    
    参数:
        app_key: 阿里云应用的Appkey
        token: 访问令牌
        audio_file: 音频文件路径
        format: 音频格式，支持MP4、AAC、MP3、OPUS、WAV，默认wav
        sample_rate: 采样率，8000(电话)或16000(非电话)，默认16000
    
    返回:
        识别结果的JSON对象
    """
    # 读取音频文件
    with open(audio_file, mode='rb') as f:
        audio_content = f.read()
    
    # 服务地址
    host = 'nls-gateway-cn-shanghai.aliyuncs.com'
    
    # 设置HTTP请求头部
    http_headers = {
        'Content-Type': 'application/octet-stream',
        'Content-Length': str(len(audio_content))
    }
    
    # 构建请求URL
    url = f'/stream/v1/FlashRecognizer?appkey={app_key}&token={token}&format={format}&sample_rate={sample_rate}'
    
    # 发送HTTPS请求
    conn = http.client.HTTPSConnection(host)
    conn.request(method='POST', url=url, body=audio_content, headers=http_headers)
    
    # 获取响应
    response = conn.getresponse()
    print(f'响应状态: {response.status} {response.reason}')
    
    body = response.read()
    conn.close()
    
    # 解析JSON响应
    try:
        result = json.loads(body)
        return result
    except ValueError:
        print('返回结果不是有效的JSON格式')
        return None

def main():
    # 这些参数需要替换为您的实际值
    app_key = 'X3DCV26B2jR45Nc4'
    token = '0d8aa5dcaca04914833d9f9d8456f5d5'
    audio_file = '您的音频文件路径.wav'  # 例如: './audio.wav'
    
    # 调用识别函数
    result = recognize_audio(app_key, token, audio_file)
    
    # 处理识别结果
    if result:
        if result.get('status') == 20000000:
            print('识别成功!')
            
            # 如果需要查看完整结果
            print('完整结果:')
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 提取识别文本
            if 'flash_result' in result and 'sentences' in result['flash_result']:
                full_text = ""
                for sentence in result['flash_result']['sentences']:
                    full_text += sentence['text']
                print(f'识别文本: {full_text}')
        else:
            print(f'识别失败: {result.get("message", "未知错误")}')

if __name__ == "__main__":
    main()